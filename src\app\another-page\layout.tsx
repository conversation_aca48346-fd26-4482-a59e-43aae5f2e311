import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Another Page - NGOAIHANG TV",
  description: "Destination page after redirect",
};

export default function AnotherPageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" className="h-full">
      <body className="min-h-full antialiased bg-white dark:bg-custom-dark transition-colors duration-200">
        {/* No header, footer, or other layout components */}
        <main className="min-h-screen">
          {children}
        </main>
      </body>
    </html>
  );
}

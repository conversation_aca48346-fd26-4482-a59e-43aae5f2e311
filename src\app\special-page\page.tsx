"use client";

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export default function SpecialPage() {
  const searchParams = useSearchParams();
  
  useEffect(() => {
    // This logic is also handled in middleware, but keeping it here as backup
    const source = searchParams.get('source');
    if (source === '1') {
      window.location.href = '/another-page';
    }
  }, [searchParams]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          Special Page
        </h1>
        <p className="text-gray-600 mb-6">
          Đây là trang đặc biệt với layout riêng, không có header và footer.
        </p>
        <div className="space-y-4">
          <p className="text-sm text-gray-500">
            Thử các link sau để test redirect:
          </p>
          <div className="space-y-2">
            <a 
              href="/special-page?source=1" 
              className="block w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Test Redirect (source=1)
            </a>
            <a 
              href="/special-page?source=2" 
              className="block w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              No Redirect (source=2)
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

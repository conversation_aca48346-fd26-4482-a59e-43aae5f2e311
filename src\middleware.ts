import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  // Middleware hiện tại không thực hiện redirect nào
  // Chỉ để lại để có thể thêm logic khác trong tương lai
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};

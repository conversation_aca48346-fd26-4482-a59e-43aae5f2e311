import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "No Layout Pages - NGOAIHANG TV",
  description: "Pages without main layout",
};

export default function NoLayoutGroup({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" className="h-full">
      <body className="min-h-full antialiased bg-white dark:bg-custom-dark transition-colors duration-200">
        {/* No header, footer, or other layout components */}
        {children}
      </body>
    </html>
  );
}

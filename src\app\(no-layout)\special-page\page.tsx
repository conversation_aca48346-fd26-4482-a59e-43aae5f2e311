"use client";

import { useSearchParams } from "next/navigation";

export default function SpecialPage() {
  const searchParams = useSearchParams();
  const source = searchParams.get("source");

  return (
    <main className="min-h-screen">
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            Special Page (No Layout)
          </h1>
          <p className="text-gray-600 mb-6">
            Đ<PERSON>y là trang đặc biệt THỰC SỰ bỏ qua layout chính, không có header và footer.
          </p>

          {/* Hiển thị thông tin query parameter */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-gray-700 mb-2">
              <strong>Query Parameter hiện tại:</strong>
            </p>
            <p className="text-lg font-mono bg-white px-3 py-2 rounded border">
              source = {source || "không có"}
            </p>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              Thử các link sau để test các query parameter khác nhau:
            </p>
            <div className="space-y-2">
              <a
                href="/special-page?source=1"
                className="block w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Test với source=1
              </a>
              <a
                href="/special-page?source=2"
                className="block w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                Test với source=2
              </a>
              <a
                href="/special-page"
                className="block w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Không có query parameter
              </a>
              <a
                href="/"
                className="block w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
              >
                Về trang chủ (có layout)
              </a>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Special Page - NGOAIHANG TV",
  description: "Special page with custom layout",
};

export default function SpecialPageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" className="h-full">
      <body className="min-h-full antialiased bg-white dark:bg-custom-dark transition-colors duration-200">
        {/* No header, footer, or other layout components */}
        <main className="min-h-screen">
          {children}
        </main>
      </body>
    </html>
  );
}
